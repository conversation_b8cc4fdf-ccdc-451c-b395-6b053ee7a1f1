@extends('admin.layouts.app')

@section('title', 'Product Details')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Product Details</h1>
    <div>
        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary me-2">
            <i class="fas fa-edit me-2"></i>Edit Product
        </a>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Products
        </a>
    </div>
</div>

<div class="row">
    <!-- Product Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Name:</strong></div>
                    <div class="col-sm-9">{{ $product->name }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>SKU:</strong></div>
                    <div class="col-sm-9">{{ $product->sku }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Category:</strong></div>
                    <div class="col-sm-9">{{ $product->category->name }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Status:</strong></div>
                    <div class="col-sm-9">
                        <span class="badge bg-{{ $product->status === 'active' ? 'success' : ($product->status === 'inactive' ? 'danger' : 'warning') }}">
                            {{ ucfirst($product->status) }}
                        </span>
                        @if($product->is_featured)
                            <span class="badge bg-info ms-2">Featured</span>
                        @endif
                    </div>
                </div>
                
                @if($product->short_description)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Short Description:</strong></div>
                    <div class="col-sm-9">{{ $product->short_description }}</div>
                </div>
                @endif
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Description:</strong></div>
                    <div class="col-sm-9">{!! nl2br(e($product->description)) !!}</div>
                </div>
            </div>
        </div>

        <!-- Pricing Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Pricing</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Regular Price:</strong></div>
                    <div class="col-sm-9">
                        <span class="h5 {{ $product->is_on_sale ? 'text-decoration-line-through text-muted' : '' }}">
                            Rp {{ number_format($product->price, 0, ',', '.') }}
                        </span>
                    </div>
                </div>
                
                @if($product->sale_price)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Sale Price:</strong></div>
                    <div class="col-sm-9">
                        <span class="h5 text-danger">
                            Rp {{ number_format($product->sale_price, 0, ',', '.') }}
                        </span>
                        <span class="badge bg-success ms-2">
                            {{ round((($product->price - $product->sale_price) / $product->price) * 100) }}% OFF
                        </span>
                    </div>
                </div>
                @endif
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Effective Price:</strong></div>
                    <div class="col-sm-9">
                        <span class="h4 text-primary">
                            Rp {{ number_format($product->effective_price, 0, ',', '.') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Inventory</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Stock Management:</strong></div>
                    <div class="col-sm-9">
                        <span class="badge bg-{{ $product->manage_stock ? 'success' : 'secondary' }}">
                            {{ $product->manage_stock ? 'Enabled' : 'Disabled' }}
                        </span>
                    </div>
                </div>
                
                @if($product->manage_stock)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Stock Quantity:</strong></div>
                    <div class="col-sm-9">
                        <span class="badge bg-{{ $product->stock_quantity <= 10 ? ($product->stock_quantity == 0 ? 'danger' : 'warning') : 'success' }} fs-6">
                            {{ $product->stock_quantity }} units
                        </span>
                        @if($product->stock_quantity <= 10 && $product->stock_quantity > 0)
                            <small class="text-warning ms-2">Low stock alert!</small>
                        @elseif($product->stock_quantity == 0)
                            <small class="text-danger ms-2">Out of stock!</small>
                        @endif
                    </div>
                </div>
                @endif
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>In Stock:</strong></div>
                    <div class="col-sm-9">
                        <span class="badge bg-{{ $product->in_stock ? 'success' : 'danger' }}">
                            {{ $product->in_stock ? 'Yes' : 'No' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Shipping</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Weight:</strong></div>
                    <div class="col-sm-9">{{ $product->weight ? $product->weight . ' kg' : 'Not specified' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Dimensions:</strong></div>
                    <div class="col-sm-9">{{ $product->dimensions ?: 'Not specified' }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Product Images -->
        @if($product->images && count($product->images) > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Product Images</h5>
            </div>
            <div class="card-body">
                <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($product->images as $index => $image)
                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                <img src="{{ Storage::url($image) }}" class="d-block w-100 rounded" 
                                     style="height: 300px; object-fit: cover;" alt="Product Image">
                            </div>
                        @endforeach
                    </div>
                    @if(count($product->images) > 1)
                        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                    @endif
                </div>
                
                @if(count($product->images) > 1)
                <div class="row g-2 mt-2">
                    @foreach($product->images as $index => $image)
                        <div class="col-3">
                            <img src="{{ Storage::url($image) }}" class="img-thumbnail" 
                                 style="height: 60px; object-fit: cover; cursor: pointer;"
                                 onclick="document.querySelector('#productCarousel .carousel-item:nth-child({{ $index + 1 }})').click()"
                                 alt="Thumbnail">
                        </div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
        @else
        <div class="card mb-4">
            <div class="card-body text-center">
                <i class="fas fa-image fa-3x text-muted mb-3"></i>
                <p class="text-muted">No images available</p>
            </div>
        </div>
        @endif

        <!-- Quick Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ $product->orderItems()->sum('quantity') }}</h4>
                            <small class="text-muted">Total Sold</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ $product->cartItems()->count() }}</h4>
                        <small class="text-muted">In Carts</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card">
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Product
                    </a>
                    <form action="{{ route('admin.products.destroy', $product) }}" method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this product?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Timestamps -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Timestamps</h5>
            </div>
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-sm-5"><strong>Created:</strong></div>
                    <div class="col-sm-7">{{ $product->created_at->format('M j, Y g:i A') }}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Updated:</strong></div>
                    <div class="col-sm-7">{{ $product->updated_at->format('M j, Y g:i A') }}</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@extends('admin.layouts.app')

@section('title', 'Categories')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">Categories</h1>
    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>Add Category
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.categories.index') }}">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Search categories...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="parent" class="form-label">Parent Category</label>
                    <select class="form-select" id="parent" name="parent">
                        <option value="">All Categories</option>
                        <option value="root" {{ request('parent') === 'root' ? 'selected' : '' }}>Root Categories Only</option>
                        @foreach($parentCategories as $parent)
                            <option value="{{ $parent->id }}" {{ request('parent') == $parent->id ? 'selected' : '' }}>
                                {{ $parent->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Categories Table -->
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Image</th>
                        <th>Category</th>
                        <th>Parent</th>
                        <th>Products</th>
                        <th>Sort Order</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $category)
                        <tr>
                            <td>
                                @if($category->image)
                                    <img src="{{ Storage::url($category->image) }}" 
                                         alt="{{ $category->name }}" 
                                         class="rounded" 
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                @else
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-folder text-muted"></i>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div>
                                    <h6 class="mb-1">{{ $category->name }}</h6>
                                    <small class="text-muted">{{ $category->slug }}</small>
                                    @if($category->children_count > 0)
                                        <br><small class="text-info">{{ $category->children_count }} subcategories</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($category->parent)
                                    <span class="badge bg-secondary">{{ $category->parent->name }}</span>
                                @else
                                    <span class="text-muted">Root Category</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $category->products_count ?? 0 }} products</span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark">{{ $category->sort_order }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $category->is_active ? 'success' : 'danger' }}">
                                    {{ $category->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.categories.show', $category) }}" 
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.categories.edit', $category) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('admin.categories.destroy', $category) }}" 
                                          method="POST" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this category?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center py-4 text-muted">
                                <i class="fas fa-folder fa-3x mb-3"></i><br>
                                No categories found
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($categories->hasPages())
        <div class="card-footer">
            {{ $categories->links() }}
        </div>
    @endif
</div>

<!-- Category Hierarchy -->
@if($categories->where('parent_id', null)->count() > 0)
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Category Hierarchy</h5>
    </div>
    <div class="card-body">
        <div class="category-tree">
            @foreach($categories->where('parent_id', null) as $rootCategory)
                <div class="category-node">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-folder me-2 text-primary"></i>
                        <strong>{{ $rootCategory->name }}</strong>
                        <span class="badge bg-{{ $rootCategory->is_active ? 'success' : 'danger' }} ms-2">
                            {{ $rootCategory->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    @if($rootCategory->children->count() > 0)
                        <div class="ms-4">
                            @foreach($rootCategory->children as $child)
                                <div class="d-flex align-items-center mb-1">
                                    <i class="fas fa-folder-open me-2 text-secondary"></i>
                                    {{ $child->name }}
                                    <span class="badge bg-{{ $child->is_active ? 'success' : 'danger' }} ms-2">
                                        {{ $child->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
@endif
@endsection

@push('styles')
<style>
.category-tree {
    font-family: monospace;
}
.category-node {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-left: 3px solid #dee2e6;
}
</style>
@endpush

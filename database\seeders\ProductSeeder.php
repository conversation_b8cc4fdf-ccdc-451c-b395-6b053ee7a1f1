<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();

        if ($categories->count() === 0) {
            $this->command->info('No categories found. Please run CategorySeeder first.');
            return;
        }

        $products = [
            [
                'name' => 'Smartphone Samsung Galaxy S24',
                'description' => 'Latest Samsung Galaxy S24 with advanced camera system and powerful processor. Features include 6.2-inch Dynamic AMOLED display, 50MP triple camera, and 4000mAh battery.',
                'short_description' => 'Latest Samsung Galaxy S24 with advanced features',
                'price' => 12000000,
                'sale_price' => 10500000,
                'stock_quantity' => 25,
                'category_id' => $categories->where('name', 'Electronics')->first()->id ?? 1,
                'status' => 'active',
                'is_featured' => true,
            ],
            [
                'name' => 'Laptop ASUS ROG Strix G15',
                'description' => 'Gaming laptop with AMD Ryzen 7 processor, NVIDIA RTX 3060 graphics, 16GB RAM, and 512GB SSD. Perfect for gaming and professional work.',
                'short_description' => 'High-performance gaming laptop',
                'price' => 18000000,
                'stock_quantity' => 15,
                'category_id' => $categories->where('name', 'Electronics')->first()->id ?? 1,
                'status' => 'active',
                'is_featured' => true,
            ],
            [
                'name' => 'Men\'s Cotton T-Shirt',
                'description' => 'Comfortable 100% cotton t-shirt available in multiple colors. Perfect for casual wear with a relaxed fit.',
                'short_description' => 'Comfortable 100% cotton t-shirt',
                'price' => 150000,
                'sale_price' => 120000,
                'stock_quantity' => 50,
                'category_id' => $categories->where('name', 'Clothing')->first()->id ?? 2,
                'status' => 'active',
            ],
            [
                'name' => 'Programming Book: Clean Code',
                'description' => 'A comprehensive guide to writing clean, maintainable code. Essential reading for software developers.',
                'short_description' => 'Essential programming book for developers',
                'price' => 350000,
                'stock_quantity' => 30,
                'category_id' => $categories->where('name', 'Books')->first()->id ?? 3,
                'status' => 'active',
            ],
            [
                'name' => 'Wireless Bluetooth Headphones',
                'description' => 'Premium wireless headphones with noise cancellation, 30-hour battery life, and superior sound quality.',
                'short_description' => 'Premium wireless headphones with noise cancellation',
                'price' => 2500000,
                'stock_quantity' => 8,
                'category_id' => $categories->where('name', 'Electronics')->first()->id ?? 1,
                'status' => 'active',
                'is_featured' => true,
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }

        $this->command->info('Sample products created successfully!');
    }
}
